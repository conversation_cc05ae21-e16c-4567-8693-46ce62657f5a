import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { Sale } from "../server/Sale";

export const useDeleteSale = () => {
  const queryClient = useQueryClient();

  return useMutation<any, Error, string>({
    mutationFn: async (saleId: string) => {
      const res = await Sale.deleteSale(saleId);
      return res;
    },
    onSuccess: (data) => {
      toast.success("Sale deleted successfully!");
      
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ["sales"] });
    },
    onError: (error: any) => {
      console.error("Error deleting sale:", error);
      const errorMessage = error?.response?.data?.message || error.message || "Failed to delete sale";
      toast.error(errorMessage);
    },
  });
};
