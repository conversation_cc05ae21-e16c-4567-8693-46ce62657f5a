import { z } from "zod";

export const createUserSchema = z.object({
    name: z.string(),
    phone: z.string(),
    email: z.string(),
    role: z.string(),
    organization_id: z.string(),
});

export const roleTeamSchema = z.object({
    role_id: z.number(),
    team_id: z.number()
});

export const assignRolesToUserSchema = z.object({
    roles: z.array(
      z.object({
        role_id: z.number(),
        team_id: z.number(),
      })
    ),
  })