"use client";

import { cn } from "@/lib/utils";
import { 
  Store, 
  History, 
  CreditCard, 
  Bell, 
  Shield, 
  Receipt, 
  Users, 
  Puzzle,
  ChevronRight
} from "lucide-react";
import { SettingsSection } from "@/app/(dashboard)/settings/page";

interface SettingsNavigationProps {
  activeSection: SettingsSection;
  onSectionChange: (section: SettingsSection) => void;
  isMobile?: boolean;
}

const navigationItems = [
  {
    id: "store-details" as SettingsSection,
    label: "Store Details",
    description: "Manage your store information",
    icon: Store,
  },
  {
    id: "order-history" as SettingsSection,
    label: "Order History",
    description: "View and manage orders",
    icon: History,
  },
  {
    id: "transactions" as SettingsSection,
    label: "Transactions",
    description: "Payment and transaction history",
    icon: CreditCard,
  },
  {
    id: "security" as SettingsSection,
    label: "Security",
    description: "Password and security settings",
    icon: Shield,
  },
];

export const SettingsNavigation = ({
  activeSection,
  onSectionChange,
  isMobile = false
}: SettingsNavigationProps) => {
  return (
    <div className={cn(
      "bg-white rounded-lg shadow-sm border border-gray-200",
      isMobile ? "w-full" : ""
    )}>
      <div className={cn(
        isMobile ? "p-4" : "p-6"
      )}>
        {!isMobile && (
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Settings</h2>
        )}
        <nav className="space-y-2">
          {navigationItems.map((item) => {
            const Icon = item.icon;
            const isActive = activeSection === item.id;

            return (
              <button
                key={item.id}
                onClick={() => onSectionChange(item.id)}
                className={cn(
                  "w-full flex items-center gap-3 px-4 py-4 rounded-lg text-left transition-all duration-200 group",
                  isActive
                    ? "bg-primary text-white shadow-md"
                    : "text-gray-700 hover:bg-gray-50 hover:text-gray-900",
                  isMobile ? "border border-gray-100 mb-2" : ""
                )}
              >
                <Icon className={cn(
                  "h-5 w-5 flex-shrink-0 transition-colors",
                  isActive ? "text-white" : "text-gray-400 group-hover:text-gray-600"
                )} />

                <div className="flex-1 min-w-0">
                  <div className={cn(
                    "font-medium",
                    isMobile ? "text-base" : "text-sm",
                    isActive ? "text-white" : "text-gray-900"
                  )}>
                    {item.label}
                  </div>
                  {!isMobile && (
                    <div className={cn(
                      "text-xs mt-0.5 truncate",
                      isActive ? "text-blue-100" : "text-gray-500"
                    )}>
                      {item.description}
                    </div>
                  )}
                  {isMobile && (
                    <div className={cn(
                      "text-sm mt-1 truncate",
                      isActive ? "text-blue-100" : "text-gray-500"
                    )}>
                      {item.description}
                    </div>
                  )}
                </div>

                {isMobile && (
                  <ChevronRight className={cn(
                    "h-5 w-5 flex-shrink-0",
                    isActive ? "text-white" : "text-gray-400"
                  )} />
                )}
              </button>
            );
          })}
        </nav>
      </div>
    </div>
  );
};
