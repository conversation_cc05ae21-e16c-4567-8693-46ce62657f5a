"use client";
import { ColumnDef } from "@tanstack/react-table";
import { dateDiff, formatDate } from "@/lib/utils";
import { Checkbox } from "@/components/ui/checkbox";
import { Button } from "@/components/ui/button";
import { ArrowUpDown } from "lucide-react";
import { Customers } from "./types/customers";
import { CustomerTableActions } from "./components/customer-table-actions";

export const customerColumns = (organization_id: string): ColumnDef<Customers>[] => [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },

  {
    accessorKey: "name",
    header: ({column})=>(
      <div className="flex items-center">
      <div>Name</div>
      <Button
        variant="ghost"
        size="icon"
        onClick={() => column.toggleSorting()}
      >
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    </div>
    ),
    enableSorting:true
  },

  {
    accessorKey: "phone",
    header:({column})=>(
      <div className="flex items-center">
      <div>Phone</div>
      <Button
        variant="ghost"
        size="icon"
        onClick={() => column.toggleSorting()}
      >
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    </div>
    ),
    enableSorting:true
  },

  {
    accessorKey: "email",
    header: ({column})=>(
      <div className="flex items-center">
      <div>Email</div>
      <Button
        variant="ghost"
        size="icon"
        onClick={() => column.toggleSorting()}
      >
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
      </div>
    ),
    enableSorting:true
  },

  {
    accessorKey: "created_at",
    header:({column})=>(
      <div className="flex items-center">
      <div>Created</div>
      <Button
        variant="ghost"
        size="icon"
        onClick={() => column.toggleSorting()}
      >
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
      </div>

    ),
    enableSorting:true,
    cell: ({ row }) => {
      return formatDate(row.original.created_at);
    },
  },
  {
    accessorKey: "updated_at",
    header:({column})=>(
      <div className="flex items-center">
      <div>Updated</div>
      <Button
        variant="ghost"
        size="icon"
        onClick={() => column.toggleSorting()}
      >
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
      </div>

    ),
    enableSorting:true,
    cell: ({ row }) => {
      return dateDiff(row.original.updated_at);
    },
  },

  {
    
    accessorKey: "actions",
    header: "Actions",
    cell: ({ row }) => {
      const customer = row.original;
      return <CustomerTableActions customer={customer} organization_id={organization_id}/>
    },
  },


];