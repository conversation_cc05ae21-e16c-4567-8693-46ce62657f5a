import Image from "next/image";
import Link from "next/link";
import { DottedSeparator } from "./dotted-separator";
import { Navigation } from "./navigation";
import { useStateManager } from "@/hooks/use-context";
import { usePathname } from "next/navigation";
import { Coins } from "lucide-react";

export const Sidebar = () => {
  const { currentOrganization } = useStateManager();
  const pathname = usePathname();

  return (
    <aside className="h-full bg-neutral-100 p-4 w-full shadow-md flex flex-col">
      <div className="flex justify-center">
        <Link href="/">
          <Image
            src="/logo.png"
            alt="IZI Sale Logo"
            width={120}
            height={36}
            className="mb-4 hover:scale-105 transition-transform h-10 w-auto"
          />
        </Link>
      </div>
      <DottedSeparator className="mb-4"/>
      <div className="flex-1">
        <Navigation />
      </div>

      {/* Token Display - Show only when not on root path and on small screens */}
      {pathname !== "/" && currentOrganization && (
        <div className="lg:hidden mt-4 pt-4 border-t border-neutral-200">
          <div className="flex items-center gap-2 px-3 py-2 bg-amber-50 border border-amber-200 rounded-lg">
            <Coins className="h-4 w-4 text-amber-600" />
            <span className="text-sm font-medium text-amber-700">
              {(currentOrganization as any).tokens?.toLocaleString() || 0}
            </span>
            <span className="text-xs text-amber-600">tokens</span>
          </div>
        </div>
      )}
    </aside>
  );
};