"use client";

import { SettingsSection } from "@/app/(dashboard)/settings/page";
import { StoreDetailsSettings } from "./sections/store-details-settings";
import { OrderHistorySettings } from "./sections/order-history-settings";
import { TransactionsSettings } from "./sections/transactions-settings";
import { NotificationsSettings } from "./sections/notifications-settings";
import { SecuritySettings } from "./sections/security-settings";
import { BillingSettings } from "./sections/billing-settings";
import { TeamMembersSettings } from "./sections/team-members-settings";
import { IntegrationsSettings } from "./sections/integrations-settings";

interface SettingsContentProps {
  activeSection: SettingsSection;
}

export const SettingsContent = ({ activeSection }: SettingsContentProps) => {
  const renderContent = () => {
    switch (activeSection) {
      case "store-details":
        return <StoreDetailsSettings />;
      case "order-history":
        return <OrderHistorySettings />;
      case "transactions":
        return <TransactionsSettings />;
      case "notifications":
        return <NotificationsSettings />;
      case "security":
        return <SecuritySettings />;
      case "billing":
        return <BillingSettings />;
      case "team-members":
        return <TeamMembersSettings />;
      case "integrations":
        return <IntegrationsSettings />;
      default:
        return <StoreDetailsSettings />;
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      <div className="p-6">
        {renderContent()}
      </div>
    </div>
  );
};
