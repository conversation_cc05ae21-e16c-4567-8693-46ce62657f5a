"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from "@/components/ui/card";
import { <PERSON>ton } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Puzzle, ExternalLink } from "lucide-react";

const mockIntegrations = [
  {
    id: 1,
    name: "M-<PERSON><PERSON><PERSON>",
    description: "Mobile payment integration for Tanzania",
    status: "connected",
    icon: "💳",
  },
  {
    id: 2,
    name: "WhatsApp Business",
    description: "Send order updates via WhatsApp",
    status: "available",
    icon: "💬",
  },
  {
    id: 3,
    name: "Google Analytics",
    description: "Track website and sales analytics",
    status: "connected",
    icon: "📊",
  },
  {
    id: 4,
    name: "Mailchimp",
    description: "Email marketing and newsletters",
    status: "available",
    icon: "📧",
  },
];

export const IntegrationsSettings = () => {
  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-gray-900">Integrations</h2>
        <p className="text-gray-600 mt-1">Connect third-party apps and services</p>
      </div>

      {/* Connected Integrations */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Puzzle className="h-5 w-5" />
            Available Integrations
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {mockIntegrations.map((integration) => (
              <div
                key={integration.id}
                className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center gap-3">
                    <div className="text-2xl">{integration.icon}</div>
                    <div>
                      <h3 className="font-medium text-gray-900">{integration.name}</h3>
                      <p className="text-sm text-gray-500">{integration.description}</p>
                    </div>
                  </div>
                  <Badge variant={integration.status === "connected" ? "default" : "secondary"}>
                    {integration.status}
                  </Badge>
                </div>

                <div className="flex items-center justify-between">
                  {integration.status === "connected" ? (
                    <div className="flex items-center gap-2">
                      <Switch defaultChecked />
                      <span className="text-sm text-gray-600">Enabled</span>
                    </div>
                  ) : (
                    <div />
                  )}

                  <Button
                    variant={integration.status === "connected" ? "outline" : "default"}
                    size="sm"
                  >
                    {integration.status === "connected" ? "Configure" : "Connect"}
                    <ExternalLink className="h-3 w-3 ml-1" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* API Keys */}
      <Card>
        <CardHeader>
          <CardTitle>API Keys</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="p-4 bg-gray-50 rounded-lg">
              <h3 className="font-medium text-gray-900 mb-2">Public API Key</h3>
              <code className="text-sm bg-white p-2 rounded border block">
                pk_live_51234567890abcdef...
              </code>
              <Button variant="outline" size="sm" className="mt-2">
                Copy Key
              </Button>
            </div>
            <div className="p-4 bg-gray-50 rounded-lg">
              <h3 className="font-medium text-gray-900 mb-2">Secret API Key</h3>
              <code className="text-sm bg-white p-2 rounded border block">
                sk_live_••••••••••••••••••••
              </code>
              <Button variant="outline" size="sm" className="mt-2">
                Reveal Key
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};