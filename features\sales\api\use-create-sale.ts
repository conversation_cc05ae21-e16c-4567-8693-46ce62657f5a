import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { Sale } from "../server/Sale";
import { CreateSaleFormData } from "../schemas";

export const useCreateSale = () => {
  const queryClient = useQueryClient();

  return useMutation<any, Error, CreateSaleFormData>({
    mutationFn: async (data: CreateSaleFormData) => {
      const res = await Sale.createSale(data);
      return res;
    },
    onSuccess: (data) => {
      toast.success("Sale created successfully!");
      
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ["sales"] });
    },
    onError: (error: any) => {
      console.error("Error creating sale:", error);
      const errorMessage = error?.response?.data?.message || error.message || "Failed to create sale";
      toast.error(errorMessage);
    },
  });
};
