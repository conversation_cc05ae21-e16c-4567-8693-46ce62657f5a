import axios from "axios";
import <PERSON>zi<PERSON><PERSON> from "@/lib/endpoints";
import { FetchSalesRequest } from "../types/sales";
import { CreateSaleFormData } from "../schemas";

export class Sale {
    private static token = localStorage.getItem("izi_token");

    public static async getAllSales(request: FetchSalesRequest) {
        try {
            const response = await axios.get(IziApi.sales, {
                params: request,
                headers: {
                    Authorization: `Bearer ${this.token}`,
                },
            });

            if (response.status === 200) {
                return response.data; 
            }
        } catch (error: any) {
            throw error.response.data; 
        }
    }

    public static async createSale(formData: CreateSaleFormData) {
        try {
            const token = localStorage.getItem("izi_token");
            
            if (!token) {
                throw new Error("Authentication token not found");
            }

            const response = await axios.post(IziApi.sales, formData, {
                headers: {
                    Authorization: `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (response.status === 200 || response.status === 201) {
                return response.data.data; 
            }
        } catch (error: any) {
            console.error("Sale creation error:", error);
            throw error.response?.data || error.message; 
        }
    }

    public static async getSaleById(id: string) {
        try {
            const token = localStorage.getItem("izi_token");
            
            if (!token) {
                throw new Error("Authentication token not found");
            }

            const response = await axios.get(`${IziApi.sales}/${id}`, {
                headers: {
                    Authorization: `Bearer ${token}`,
                },
            });

            if (response.status === 200) {
                return response.data.data; 
            }
        } catch (error: any) {
            console.error("Sale fetch error:", error);
            throw error.response?.data || error.message; 
        }
    }

    public static async updateSale(id: string, formData: Partial<CreateSaleFormData>) {
        try {
            const token = localStorage.getItem("izi_token");
            
            if (!token) {
                throw new Error("Authentication token not found");
            }

            const response = await axios.put(`${IziApi.sales}/${id}`, formData, {
                headers: {
                    Authorization: `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (response.status === 200) {
                return response.data.data; 
            }
        } catch (error: any) {
            console.error("Sale update error:", error);
            throw error.response?.data || error.message; 
        }
    }

    public static async deleteSale(id: string) {
        try {
            const token = localStorage.getItem("izi_token");
            
            if (!token) {
                throw new Error("Authentication token not found");
            }

            const response = await axios.delete(`${IziApi.sales}/${id}`, {
                headers: {
                    Authorization: `Bearer ${token}`,
                },
            });

            if (response.status === 200) {
                return response.data; 
            }
        } catch (error: any) {
            console.error("Sale delete error:", error);
            throw error.response?.data || error.message; 
        }
    }
}
