import { useQuery } from "@tanstack/react-query";
import { FetchSalesRequest } from "../types/sales";
import { Sale } from "../server/Sale";

export const useFetchSales = (filters?: FetchSalesRequest)  => {
 const _filters = filters ?? {};

  return useQuery({
    queryKey: ["sales", _filters],
    queryFn: () => Sale.getAllSales({
        search: _filters.search,
        start_date: _filters.start_date,
        end_date: _filters.end_date,
        agent_id: _filters.agent_id,
        with_trashed: _filters.with_trashed,
        page: _filters.page,
        organization_id: _filters.organization_id,
    }),
 });
};
