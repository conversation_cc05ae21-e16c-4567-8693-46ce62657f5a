"use client";

import { ColumnDef } from "@tanstack/react-table";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { Sale } from "./types/sales";
import { SaleTableActions } from "./components/sale-table-actions";
import { format } from "date-fns";

export const saleColumns = (organization_id: string): ColumnDef<Sale>[] => [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "code",
    header: "Sale Code",
    cell: ({ row }) => (
      <div className="font-medium text-gray-900">
        {row.getValue("code")}
      </div>
    ),
  },
  {
    accessorKey: "customer",
    header: "Customer",
    cell: ({ row }) => {
      const customer = row.getValue("customer") as any;
      return (
        <div className="flex flex-col">
          <span className="font-medium text-gray-900">{customer?.name}</span>
          <span className="text-sm text-gray-500">{customer?.phone}</span>
        </div>
      );
    },
  },
  {
    accessorKey: "total_amount",
    header: "Total Amount",
    cell: ({ row }) => {
      const amount = parseFloat(row.getValue("total_amount"));
      return (
        <div className="font-medium text-green-600">
          TSH {amount.toLocaleString('en-US', { 
            minimumFractionDigits: 2, 
            maximumFractionDigits: 2 
          })}/=
        </div>
      );
    },
  },
  {
    accessorKey: "received_amount",
    header: "Received",
    cell: ({ row }) => {
      const amount = parseFloat(row.getValue("received_amount"));
      return (
        <div className="font-medium text-blue-600">
          TSH {amount.toLocaleString('en-US', { 
            minimumFractionDigits: 2, 
            maximumFractionDigits: 2 
          })}/=
        </div>
      );
    },
  },
  {
    accessorKey: "status",
    header: "Status",
    cell: ({ row }) => {
      const status = row.getValue("status") as string;
      const getStatusColor = (status: string) => {
        switch (status.toLowerCase()) {
          case "completed":
            return "bg-green-100 text-green-800 border-green-200";
          case "pending":
            return "bg-yellow-100 text-yellow-800 border-yellow-200";
          case "cancelled":
            return "bg-red-100 text-red-800 border-red-200";
          default:
            return "bg-gray-100 text-gray-800 border-gray-200";
        }
      };

      return (
        <Badge 
          variant="outline" 
          className={`${getStatusColor(status)} capitalize`}
        >
          {status}
        </Badge>
      );
    },
  },
  {
    accessorKey: "is_credit_sale",
    header: "Type",
    cell: ({ row }) => {
      const isCredit = row.getValue("is_credit_sale") as boolean;
      return (
        <Badge 
          variant={isCredit ? "destructive" : "default"}
          className={isCredit ? "bg-orange-100 text-orange-800 border-orange-200" : ""}
        >
          {isCredit ? "Credit" : "Cash"}
        </Badge>
      );
    },
  },
  {
    accessorKey: "date",
    header: "Sale Date",
    cell: ({ row }) => {
      const date = row.getValue("date") as string;
      return (
        <div className="text-gray-600">
          {format(new Date(date), "MMM dd, yyyy")}
        </div>
      );
    },
  },
  {
    accessorKey: "user",
    header: "Sales Agent",
    cell: ({ row }) => {
      const user = row.getValue("user") as any;
      return (
        <div className="text-gray-600">
          {user?.name || "N/A"}
        </div>
      );
    },
  },
  {
    id: "actions",
    header: "Actions",
    cell: ({ row }) => (
      <SaleTableActions 
        sale={row.original} 
        organization_id={organization_id}
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
];
