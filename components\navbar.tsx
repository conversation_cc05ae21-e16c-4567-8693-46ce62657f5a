import { User<PERSON>utton } from "@/features/auth/components/user-button";
import { MobileSidebar } from "./mobile-sidebar";
import { OrganizationSelector } from "./organization-selector";
import { useStateManager } from "@/hooks/use-context";
import { usePathname } from "next/navigation";
import { Coins } from "lucide-react";

export const Navbar = () => {
    const { currentOrganization } = useStateManager();
    const pathname = usePathname();

    // Parse organizations from localStorage
    const organizationsRaw = localStorage.getItem("organizations");
    let organizations;
    try {
        organizations = organizationsRaw ? JSON.parse(organizationsRaw) : undefined;
    } catch {
        organizations = undefined;
    }

    return (
        <nav className="py-4 px-6 flex items-center justify-between fixed sm:relative top-0 w-full z-50 bg-white">
            <div className="flex items-center gap-6">
                <div className="flex-col hidden lg:flex">
                    <h1 className="text-2xl font-semibold">Dashboard</h1>
                    <p className="text-muted-foreground">
                        Monitor your business operations
                    </p>
                </div>


                <MobileSidebar />
            </div>

            <div className="flex items-center  gap-4">
                {/* Organization Selector */}
                <OrganizationSelector
                    currentOrganization={currentOrganization || undefined}
                    organizations={organizations}
                />

                {/* Token Display - Show only when not on root path and on large screens */}
                {pathname !== "/" && currentOrganization && (
                    <div className="hidden lg:flex items-center gap-2 px-3 py-2 bg-amber-50 border border-amber-200 rounded-lg">
                        <Coins className="h-4 w-4 text-amber-600" />
                        <span className="text-sm font-medium text-amber-700">
                            {(currentOrganization as any).tokens?.toLocaleString() || 0}
                        </span>
                        <span className="text-xs text-amber-600">tokens</span>
                    </div>
                )}

                <UserButton />
            </div>
        </nav>
    );
}