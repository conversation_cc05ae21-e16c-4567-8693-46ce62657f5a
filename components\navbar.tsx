import { UserButton } from "@/features/auth/components/user-button";
import { MobileSidebar } from "./mobile-sidebar";
import { OrganizationSelector } from "./organization-selector";
import { useStateManager } from "@/hooks/use-context";
import { usePathname } from "next/navigation";

export const Navbar = () => {
    const { currentOrganization } = useStateManager();
    const pathname = usePathname();

    const organizations = localStorage.getItem("organizations");

    return (
        <nav className="py-4 px-6 flex items-center justify-between fixed sm:relative top-0 w-full z-50 bg-white">
            <div className="flex items-center gap-6">
                <div className="flex-col hidden lg:flex">
                    <h1 className="text-2xl font-semibold">Dashboard</h1>
                    <p className="text-muted-foreground">
                        Monitor your business operations
                    </p>
                </div>

                
                <MobileSidebar />
            </div>

            <div className="flex items-center  gap-4">
                {/* Organization Selector */}
                <OrganizationSelector
                    currentOrganization={currentOrganization || undefined}
                    organizations={organizations}
                />
                <UserButton />
            </div>
        </nav>
    );
}