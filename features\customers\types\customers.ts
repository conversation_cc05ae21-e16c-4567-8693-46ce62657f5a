import z from "zod";
import { fetchCustomersSchema } from "../schemas";

export interface Customers {
    id:number;            
    name:string;         
    address :string;      
    phone :string;        
    email :string;         
    deleted_at:string;   
    created_at :string;   
    updated_at :string ;  
    bus_name  :string ;   
    bus_address :string;  
    bus_phone :string;   
    bus_tin  :string ;  
    bus_vrn  :string ;  
    organization_id:string;
}

export type FetchCustomerRequest=z.infer<typeof fetchCustomersSchema>;

export interface FetchCustomerResponse{
    data:Customers[];
    total:number;
}