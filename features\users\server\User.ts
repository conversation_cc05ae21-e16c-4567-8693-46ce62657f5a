import axios from "axios";
import <PERSON>zi<PERSON><PERSON> from "@/lib/endpoints";
import { FetchUsersRequest } from "../types/users";

export class User {
    private static token = localStorage.getItem("izi_token");

    public static async getAllUses(request: FetchUsersRequest) {
        try {
            const response = await axios.get(IziApi.users, {
                params: request,
                headers: {
                    Authorization: `Bearer ${this.token}`,
                },
            });

            if (response.status === 200) {
                return response.data; 
            }
        } catch (error: any) {
            throw error.response.data; 
        }
    }
}
