import { z } from "zod";

export const fetchCustomersSchema = z.object({
    query: z.string().optional(),
    organization_id: z.string().optional(),
    with_trashed: z.boolean().optional(),
    page: z.string().optional(),
    per_page: z.string().optional(),
    total:z.string().optional()
});


export const createCustomersSchema = z.object({
    name:z.string().min(4,"Name required more than 4 characters"),         
    address :z.string().optional(),    
    phone :z.string().max(12,"Phone number required"),       
    email: z.string().email("Invalid email address").optional(),        
    bus_name  :z.string().default('').optional(), 
    bus_address :z.string().default('').optional(),
    bus_phone :z.string().default('').optional(),  
    bus_tin  :z.string().default('').optional(),
    bus_vrn  :z.string().default('').optional(),
    organization_id:z.string(),
    
});