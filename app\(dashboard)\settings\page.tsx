"use client";

import { useState } from "react";
import { SettingsNavigation } from "@/features/settings/components/settings-navigation";
import { SettingsContent } from "@/features/settings/components/settings-content";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";

export type SettingsSection = 
  | "store-details"
  | "order-history" 
  | "transactions"
  | "notifications"
  | "security"
  | "billing"
  | "team-members"
  | "integrations";

const SettingsPage = () => {
  const [activeSection, setActiveSection] = useState<SettingsSection>("store-details");
  const [isMobileContentOpen, setIsMobileContentOpen] = useState(false);

  const handleSectionChange = (section: SettingsSection) => {
    setActiveSection(section);
    setIsMobileContentOpen(true);
  };

  const handleMobileBack = () => {
    setIsMobileContentOpen(false);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Page Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Settings</h1>
          <p className="text-gray-600 mt-2">Manage your store settings and preferences</p>
        </div>

        {/* Desktop Layout */}
        <div className="hidden lg:grid lg:grid-cols-12 lg:gap-8">
          {/* Navigation Sidebar */}
          <div className="lg:col-span-3">
            <SettingsNavigation 
              activeSection={activeSection}
              onSectionChange={handleSectionChange}
            />
          </div>

          {/* Content Area */}
          <div className="lg:col-span-9">
            <SettingsContent activeSection={activeSection} />
          </div>
        </div>

        {/* Mobile Layout */}
        <div className="lg:hidden">
          <div className="relative overflow-hidden">
            {/* Navigation Panel */}
            <div className={`transition-transform duration-300 ease-in-out ${
              isMobileContentOpen ? '-translate-x-full' : 'translate-x-0'
            }`}>
              <SettingsNavigation 
                activeSection={activeSection}
                onSectionChange={handleSectionChange}
                isMobile={true}
              />
            </div>

            {/* Content Panel */}
            <div className={`absolute top-0 left-0 w-full transition-transform duration-300 ease-in-out ${
              isMobileContentOpen ? 'translate-x-0' : 'translate-x-full'
            }`}>
              <div className="bg-white rounded-lg shadow-sm">
                {/* Mobile Header with Back Button */}
                <div className="flex items-center gap-3 p-4 border-b border-gray-200">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleMobileBack}
                    className="p-2"
                  >
                    <ArrowLeft className="h-5 w-5" />
                  </Button>
                  <h2 className="text-lg font-semibold text-gray-900">Settings</h2>
                </div>

                {/* Mobile Content */}
                <div className="p-4">
                  <SettingsContent activeSection={activeSection} />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SettingsPage;
