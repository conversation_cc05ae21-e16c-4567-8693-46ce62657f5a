import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Edit, Loader2, MoreHorizontal, Trash } from "lucide-react";
import { Customers } from "../types/customers";

type Props = {
  customer: Customers;
  organization_id: string;
};

export const CustomerTableActions = ({ customer }: Props) => {

  const handleDelete = async () => {
    console.log('deleting customer');
  };

  return (
    <div className="flex items-center space-x-2"> 
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-8 w-8 p-0">
            <span className="sr-only">Open menu</span>
            <MoreHorizontal />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuLabel>Actions</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuGroup>
            <DropdownMenuItem
              onClick={() => {}}
            >
              <Edit /> Edit
            </DropdownMenuItem>

            <DropdownMenuItem
              className="text-red-600 flex"
            //   disabled={'false'}
              onClick={handleDelete}
            >
              <Trash color="red" /> Delete
              {/* {isPending && <Loader2 className="animate-spin ml-2" />} */}
            </DropdownMenuItem>
          </DropdownMenuGroup>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};