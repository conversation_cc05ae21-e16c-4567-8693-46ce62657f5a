import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { Sale } from "../server/Sale";
import { CreateSaleFormData } from "../schemas";

export const useUpdateSale = (saleId: string) => {
  const queryClient = useQueryClient();

  return useMutation<any, Error, Partial<CreateSaleFormData>>({
    mutationFn: async (data: Partial<CreateSaleFormData>) => {
      const res = await Sale.updateSale(saleId, data);
      return res;
    },
    onSuccess: (data) => {
      toast.success("Sale updated successfully!");
      
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ["sales"] });
      queryClient.invalidateQueries({ queryKey: ["sale", saleId] });
    },
    onError: (error: any) => {
      console.error("Error updating sale:", error);
      const errorMessage = error?.response?.data?.message || error.message || "Failed to update sale";
      toast.error(errorMessage);
    },
  });
};
