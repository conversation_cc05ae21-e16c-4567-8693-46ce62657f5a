"use client";

import { <PERSON>, CardContent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { TrendingUp, TrendingDown, DollarSign, CreditCard, Users, Calendar } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Sale } from "../types/sales";

interface SalesSummaryProps {
  sales: Sale[];
  currency?: string;
  period?: string;
}

export const SalesSummary = ({ 
  sales, 
  currency = "TSH", 
  period = "Today" 
}: SalesSummaryProps) => {
  const formatCurrency = (amount: number) => {
    return `${currency} ${amount.toLocaleString('en-US', { 
      minimumFractionDigits: 2, 
      maximumFractionDigits: 2 
    })}/=`;
  };

  // Calculate summary statistics
  const totalSales = sales.length;
  const totalAmount = sales.reduce((sum, sale) => sum + parseFloat(sale.total_amount), 0);
  const totalReceived = sales.reduce((sum, sale) => sum + parseFloat(sale.received_amount), 0);
  const creditSales = sales.filter(sale => sale.is_credit_sale).length;
  const cashSales = totalSales - creditSales;
  const uniqueCustomers = new Set(sales.map(sale => sale.customer_id)).size;

  const averageSaleAmount = totalSales > 0 ? totalAmount / totalSales : 0;

  const summaryCards = [
    {
      title: "Total Sales",
      value: totalSales.toString(),
      icon: Calendar,
      color: "text-blue-600",
      bgColor: "bg-blue-50",
      borderColor: "border-blue-200",
    },
    {
      title: "Total Amount",
      value: formatCurrency(totalAmount),
      icon: DollarSign,
      color: "text-green-600",
      bgColor: "bg-green-50",
      borderColor: "border-green-200",
    },
    {
      title: "Amount Received",
      value: formatCurrency(totalReceived),
      icon: TrendingUp,
      color: "text-emerald-600",
      bgColor: "bg-emerald-50",
      borderColor: "border-emerald-200",
    },
    {
      title: "Unique Customers",
      value: uniqueCustomers.toString(),
      icon: Users,
      color: "text-purple-600",
      bgColor: "bg-purple-50",
      borderColor: "border-purple-200",
    },
  ];

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {summaryCards.map((card, index) => (
          <Card key={index} className={`${card.bgColor} ${card.borderColor} border hover:shadow-md transition-shadow`}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">
                {card.title}
              </CardTitle>
              <card.icon className={`h-5 w-5 ${card.color}`} />
            </CardHeader>
            <CardContent>
              <div className={`text-2xl font-bold ${card.color}`}>
                {card.value}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Additional Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Sales Breakdown</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <DollarSign className="h-4 w-4 text-green-600" />
                <span className="text-sm font-medium">Cash Sales</span>
              </div>
              <div className="flex items-center space-x-2">
                <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                  {cashSales}
                </Badge>
                <span className="text-sm text-gray-500">
                  ({totalSales > 0 ? ((cashSales / totalSales) * 100).toFixed(1) : 0}%)
                </span>
              </div>
            </div>
            
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <CreditCard className="h-4 w-4 text-orange-600" />
                <span className="text-sm font-medium">Credit Sales</span>
              </div>
              <div className="flex items-center space-x-2">
                <Badge variant="outline" className="bg-orange-50 text-orange-700 border-orange-200">
                  {creditSales}
                </Badge>
                <span className="text-sm text-gray-500">
                  ({totalSales > 0 ? ((creditSales / totalSales) * 100).toFixed(1) : 0}%)
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Performance Metrics</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Average Sale Amount</span>
              <span className="text-sm font-bold text-blue-600">
                {formatCurrency(averageSaleAmount)}
              </span>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Collection Rate</span>
              <span className="text-sm font-bold text-green-600">
                {totalAmount > 0 ? ((totalReceived / totalAmount) * 100).toFixed(1) : 0}%
              </span>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Outstanding Amount</span>
              <span className="text-sm font-bold text-red-600">
                {formatCurrency(totalAmount - totalReceived)}
              </span>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
