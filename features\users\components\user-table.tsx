import { DataTable } from "@/components/data-table";
import React, { useCallback } from "react";
import { useFetchUsers } from "../api/use-fetch-users";
import { FetchUsersRequest, FetchUsersResponse } from "@/features/users/types/users";
import { userColumns } from "../columns";
import { Row, SortingState } from "@tanstack/react-table";
import { User } from "@/features/auth/types/auth";

interface UserTableProps {
  onRowSelectedChanged?: (rows: Row<User>[]) => void;
  onDataChange?: (data?: FetchUsersResponse | undefined) => void;
  searchQuery?: string;
  filters?: FetchUsersRequest;
  onSearchChange?: (value: string) => void;
  organization_id: string;
}

export const UserTable = (props: UserTableProps = {} as UserTableProps) => {
  const [request, setRequest] = React.useState<FetchUsersRequest>(props?.filters || {});
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [pagination, setPagination] = React.useState({ pageIndex: 0, pageSize: 10 });
  const [rowSelection, setRowSelection] = React.useState<Row<User>[]>([]);

  const { data: users, isLoading } = useFetchUsers(request);

  // Update request when filters or search query change
  React.useEffect(() => {
    setRequest(prev => {
      const newRequest = { ...prev };
      
      // Add filters if provided
      if (props?.filters) {
        Object.assign(newRequest, props.filters);
      }
      
      // Add search query if provided
      if (props?.searchQuery) {
        newRequest.query = props.searchQuery;
      } else if ('query' in newRequest) {
        // Remove the query property if it exists and searchQuery is empty
        delete newRequest.query;
      }
      
      return newRequest;
    });
  }, [props?.filters, props?.searchQuery]);

  // Handle sorting
  const handleSortingChange = useCallback((sortState: SortingState) => {
    setSorting(sortState);
    
    if (sortState.length > 0) {
      const { id, desc } = sortState[0];
      // Apply sorting to the request
      setRequest(prev => {
        const updatedRequest = {
          ...prev,
          sort_by: id,
          sort_direction: desc ? 'desc' as const : 'asc' as const,
        };
        return updatedRequest;
      });
    } else {
      setRequest(prev => {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const { sort_by, sort_direction, ...rest } = prev;
        return rest;
      });
    }
  }, []);

  // Effect to notify parent about row selection changes
  const { onRowSelectedChanged } = props || {};

  React.useEffect(() => {
    if (onRowSelectedChanged) {
      onRowSelectedChanged(rowSelection);
    }
  }, [rowSelection, onRowSelectedChanged]);

  const { onDataChange } = props || {};

  React.useEffect(() => {
    if (onDataChange && users){
      onDataChange(users);
    }
  }, [users, onDataChange]);

  const { onDataChange: handleDataChange } = props || {};

  React.useEffect(() => {
    if (handleDataChange && users) {
      handleDataChange(users);
    }
  }, [users, handleDataChange]);

  return (
    <DataTable
      columns={userColumns(props.organization_id)}
      data={users?.data || []}
      manualPagination={true}
      manualSorting={true}
      total={users?.total || 0}
      sorting={sorting}
      onSortingChange={handleSortingChange}
      onPaginationChange={(pg) => {
        // check if there is a change in pagination and set a new one
        if (pg.pageIndex !== pagination.pageIndex || pg.pageSize !== pagination.pageSize) {
          setPagination(pg);
          setRequest((prev) => ({
            ...prev,
            page: (pg.pageIndex + 1).toString(),
            per_page: pg.pageSize.toString(),
          }));
        }
      }}
      onRowSelectedChanged={setRowSelection}
      isLoading={isLoading}
    />
  );
};