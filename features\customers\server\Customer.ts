import axios from "axios";
import { FetchCustomerRequest } from "../types/customers";
import IziApi from "@/lib/endpoints";

export class Customer {
    private static token = localStorage.getItem("izi_token");

    public static async getAll(request: FetchCustomerRequest) {
        try {
            const response = await axios.get(IziApi.customers, {
                params: request,
                headers: {
                    Authorization: `Bearer ${this.token}`,
                },
            });

            if (response.status === 200) {
                return response.data; 
            }
        } catch (error: any) {
            throw error.response.data; 
        }
    }
}
