import { z } from "zod";
import { fetchSalesSchema } from "../schemas";
import { Customers } from "@/features/customers/types/customers";
import { Organization } from "@/features/organizations/types/organization";
import { User } from "@/features/auth/types/auth";

export interface SaleItem {
    id: string;
    sale_id: string;
    product_id: string;
    product_name: string;
    quantity: number;
    unit_price: string;
    total_price: string;
    created_at: string;
    updated_at: string;
}

export interface Sale {
    id: string;
    code: string;
    description: string | null;
    total_amount: string;
    received_amount: string;
    return_amount: string;
    status: string;
    amount_credit: string;
    date: string;

    customer_id: string;
    customer: Customers;

    organization_id: string;
    organization: Organization;

    user_id: string | null;
    user: User;

    sale_items: SaleItem[];
    is_credit_sale: boolean;
    deleted_at: string | null;
    created_at: string;
    updated_at: string;
}

// REQUEST AND RESPONSE TYPES

// Fetch sales
export type FetchSalesRequest = z.infer<typeof fetchSalesSchema>;

export interface FetchSalesResponse {
    data: Sale[];
    total: number;
}
