import { DataTable } from "@/components/data-table";
import React, { useEffect, useState } from "react";
import { Row } from "@tanstack/react-table";
import { Sale, FetchSalesRequest, FetchSalesResponse } from "../types/sales";
import { saleColumns } from "../columns";
import { useFetchSales } from "../api/use-fetch-sales";

interface SaleTableProps {
  onRowSelectedChanged?: (rows: Row<Sale>[]) => void;
  onDataChange?: (data?: FetchSalesResponse | undefined) => void;
  filters?: FetchSalesRequest;
  organization_id: string;
}

export function SaleTable({ 
  onRowSelectedChanged, 
  onDataChange, 
  filters, 
  organization_id,
}: SaleTableProps) {
  const [selectedRows, setSelectedRows] = useState<Row<Sale>[]>([]);

  const { data, isLoading, error } = useFetchSales({
    ...filters
  });

  useEffect(() => {
    if (onDataChange) {
      onDataChange(data);
    }
  }, [data, onDataChange]);

  const handleRowSelectionChange = (rows: Row<Sale>[]) => {
    setSelectedRows(rows);
    if (onRowSelectedChanged) {
      onRowSelectedChanged(rows);
    }
  };

  if (error) {
    return (
      <div className="flex items-center justify-center h-32">
        <div className="text-center">
          <p className="text-red-600 mb-2">Error loading sales</p>
          <p className="text-sm text-gray-500">
            {error instanceof Error ? error.message : "An error occurred"}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <DataTable
        columns={saleColumns(organization_id)}
        data={data?.data || []}
        isLoading={isLoading}
        onRowSelectedChanged={handleRowSelectionChange}
      />
      
      {data?.total !== undefined && (
        <div className="flex items-center justify-between text-sm text-gray-500">
          <span>
            Showing {data.data.length} of {data.total} sales
          </span>
          {selectedRows.length > 0 && (
            <span>
              {selectedRows.length} row(s) selected
            </span>
          )}
        </div>
      )}
    </div>
  );
}
