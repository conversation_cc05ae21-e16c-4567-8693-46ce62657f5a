import { useQuery } from "@tanstack/react-query";
import { FetchUsersRequest } from "../types/users";
import { User } from "../server/User";

export const useFetchUsers = (filters?: FetchUsersRequest)  => {
 const _filters = filters ?? {};

  return useQuery({
    queryKey: ["users", _filters],
    queryFn: () => User.getAllUses({
        query: _filters.query,
        role_id: _filters.role_id,
        page: _filters.page,
        per_page: _filters.per_page,
        organization_id: _filters.organization_id,
    }),
 });
};
