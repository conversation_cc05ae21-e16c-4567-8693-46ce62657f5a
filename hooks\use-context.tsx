"use client";
import { User } from '@/features/auth/types/auth';
import { ReactNode } from 'react';
import React, { createContext, useContext, useState, useEffect } from 'react';
import { Auth } from '@/features/auth/server/Auth';
import { Organization } from '@/features/organizations/types/organization';



interface ContextType {
  isAuthenticated: boolean;
  user: User | null;
  isLoading: boolean;
  setIsLoading: (value: boolean) => void;
  handleLogin: (data: any) => void;
  handleLogout: () => void;
  hasAnyOrganization: boolean;
  setHasAnyOrganization: (value: boolean) => void;
  currentOrganization: Organization | null;
  setCurrentOrganization: (org: Organization | null) => void;
}

const stateManager = createContext<ContextType>({
  isAuthenticated: false,
  user: null,
  isLoading: true,  
  setIsLoading: () => {},
  handleLogin: () => {},
  handleLogout: () => {},
  hasAnyOrganization: false,
  setHasAnyOrganization: () => {},
  currentOrganization: null,
  setCurrentOrganization: () => {},
});

export const StateManagerProvider = ({ children }: { children: ReactNode }) => {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [user, setUser] = useState<any | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [hasAnyOrganization, setHasAnyOrganization] = useState<boolean>(false);
  const [currentOrganization, setCurrentOrganization] = useState<Organization | null>(null);
  
  console.log(currentOrganization)

// checking authentication status on page load by calling the getUser API
  useEffect(() => {
    const checkAuthStatus = async () => {
      try {
        setIsLoading(true);
        const token = localStorage.getItem("izi_token");
        const currentOrganizationId = localStorage.getItem("current_organization");
        
        if (!token) {
          setIsAuthenticated(false);
          setUser(null);
          setIsLoading(false);
          return;
        };
        
        //  if(isAuthenticated){
            const response = await Auth.getUser(token);

            if (response) {
              setUser(response);
              setIsAuthenticated(true);
              setHasAnyOrganization(response.organizations.length > 0);
              localStorage.setItem("organizations", JSON.stringify(response.organizations));

              if (currentOrganizationId) {
                const currentOrganization = response.organizations.find((org: Organization) => org.id === Number(currentOrganizationId));
                setCurrentOrganization(currentOrganization);
              }
            } 
            else {
              // Invalid token, remove it
              localStorage.removeItem("izi_token");
              setIsAuthenticated(false);
              setUser(null);
            }
          // }else{
          //   return;
          // }
              
      } catch (error) {
        console.error("Auth check failed:", error);
        // Remove invalid token
        localStorage.removeItem("izi_token");
        setIsAuthenticated(false);
        setUser(null);
      } finally {
        setIsLoading(false);
      }
    };

    checkAuthStatus();
  }, []);

  const handleLogin = (data: any) => {
    localStorage.setItem("izi_token", data.token);
    setHasAnyOrganization(data.user.organizations.length > 0);
    localStorage.setItem("organizations", JSON.stringify(data.user.organizations));
    setIsAuthenticated(true);
    setUser(data.user);
    setIsLoading(false);

    if (data.user.organizations.length < 1) {
       // If user has no any organizations, redirect to create organization page
        window.location.href = "/setup/create-organization";
    } 
    // else if (data.user.organizations.length === 1) {
    //    // If user has only one organization, redirect to dashboard
    //     window.location.href = "/";
    // } 
    else {
        // If user has organizations, redirect to choose organization page
        window.location.href ="/setup/choose-organization";
    }
  };

  const handleLogout = () => {
    localStorage.removeItem("izi_token");
    setIsAuthenticated(false);
    setUser(null);
    setIsLoading(false);
    window.location.href = "/sign-in";
  };

  

  return (
    <stateManager.Provider
      value={{
        isAuthenticated,
        user,
        isLoading,
        setIsLoading,
        handleLogin,
        handleLogout,
        hasAnyOrganization,
        setHasAnyOrganization,
        currentOrganization,
        setCurrentOrganization
      }}
    >
      {children}
    </stateManager.Provider>
  );
};

export const useStateManager = () => useContext(stateManager);
