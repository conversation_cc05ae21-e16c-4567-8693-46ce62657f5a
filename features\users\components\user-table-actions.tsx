import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Edit, Eye, Loader2, MoreHorizontal, Trash, UserRoundCogIcon } from "lucide-react";
import React from "react";
import { User } from "@/features/auth/types/auth";
import { useRouter } from "next/navigation";

type Props = {
  user: User;
  organization_id: string;
};

export const UserTableActions = ({ user, organization_id }: Props) => {
  const router = useRouter();
  
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="h-8 w-8 p-0">
          <span className="sr-only">Open menu</span>
          <MoreHorizontal />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>Actions</DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuGroup>
          <DropdownMenuItem className="flex" onClick={() => router.push(`/settings/users/${user.id}`)}>
            <Eye /> View
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() =>{}}>
            <Edit /> Edit
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuGroup>
            <DropdownMenuItem className="flex" onClick={() => open(user.id?.toString())}>
              <UserRoundCogIcon /> Roles
            </DropdownMenuItem>
          </DropdownMenuGroup>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={() => {}}><Trash color="red" /> Delete 
          {/* {deleteUser.isPending && <Loader2 className="spin" />} */}
          </DropdownMenuItem>
        </DropdownMenuGroup>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};