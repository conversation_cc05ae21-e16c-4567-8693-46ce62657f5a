import { DataTable } from "@/components/data-table";
import React, { useEffect, useState } from "react";
import { Row } from "@tanstack/react-table";
import { Customers, FetchCustomerRequest, FetchCustomerResponse } from "../types/customers";
import { customerColumns } from "../columns";
import { useFetchCustomers } from "../api/use-fetch-customers";

interface CustomerTableProps {
  onRowSelectedChanged?: (rows: Row<Customers>[]) => void;
  onDataChange?: (data?: FetchCustomerResponse | undefined) => void;
  filters?: FetchCustomerRequest;
  organization_id: string;
}

export const CustomerDataTable: React.FC<CustomerTableProps> = ({
  onRowSelectedChanged,
  onDataChange,
  filters = {},
  organization_id,
}) => {
  const [request, setRequest] = useState<FetchCustomerRequest>(filters);
  const { data: customers , isLoading } = useFetchCustomers(request);
  const [pagination, setPagination] = useState({ pageIndex: 0,pageSize: 10 });
  const [rowSelection, setRowSelection] = useState<Row<Customers>[]>([]);

//   console.log(customers)
  
  useEffect(() => {
    onRowSelectedChanged?.(rowSelection);
  }, [rowSelection, onRowSelectedChanged]);

  useEffect(() => {
    onDataChange?.(customers);
  }, [customers, onDataChange]);

  useEffect(() => {
    setRequest((prev) => ({
      ...prev,
      ...filters,
    }));
  }, [filters]);

  return (
    <DataTable
      columns={customerColumns(organization_id)}
      data={customers?.data ?? []}
      manualPagination
      total={customers?.total}
      onPaginationChange={(pg) => {
        if (pg.pageIndex !== pagination.pageIndex || pg.pageSize !== pagination.pageSize) {
          setPagination(pg);
          setRequest((prev) => ({
            ...prev,
            page: (pg.pageIndex + 1).toString(),
          }));
        }
      }}
      onRowSelectedChanged={setRowSelection}
      
      isLoading={isLoading}
    />
  );
};