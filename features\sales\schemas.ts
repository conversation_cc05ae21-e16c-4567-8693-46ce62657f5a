import { z } from "zod";

export const fetchSalesSchema = z.object({
    search: z.string().optional(),
    start_date: z.string().optional(),
    end_date: z.string().optional(),
    agent_id: z.string().optional(),
    with_trashed: z.boolean().optional(),
    page: z.string().optional(),
    organization_id: z.string(),
});

export const createSaleSchema = z.object({
    code: z.string().min(1, "Sale code is required"),
    description: z.string().optional(),
    total_amount: z.string().min(1, "Total amount is required"),
    received_amount: z.string().min(1, "Received amount is required"),
    return_amount: z.string().optional().default("0"),
    status: z.string().default("completed"),
    amount_credit: z.string().optional().default("0"),
    date: z.string().min(1, "Date is required"),
    customer_id: z.string().min(1, "Customer is required"),
    organization_id: z.string().min(1, "Organization is required"),
    user_id: z.string().optional(),
    is_credit_sale: z.boolean().default(false),
    sale_items: z.array(z.object({
        product_id: z.string().min(1, "Product is required"),
        product_name: z.string().min(1, "Product name is required"),
        quantity: z.number().min(1, "Quantity must be at least 1"),
        unit_price: z.string().min(1, "Unit price is required"),
        total_price: z.string().min(1, "Total price is required"),
    })).min(1, "At least one sale item is required"),
});

export type CreateSaleFormData = z.infer<typeof createSaleSchema>;
